Vars:
  ua_accept_language: "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2"
  hitomiDb_tmp_url: "https://gitee.com/json_eri/ComicGUISpider/releases/download/v2.0.0/hitomi.db"
GUI:
  DESC1: "1、首次使用请点击<img src=\"%s\" height=\"25\" style=\"background-color: rgb(0, 255, 255);border-radius: 7px;\">按钮，配置窗口左下角点击`使用手册`，内有配置详情/GUI视频使用指南等"
  DESC2: "2、使用遇阻时可以先查阅说明中的 <a href='https://jasoneri.github.io/ComicGUISpider/faq/'>📖FAQ/额外说明</a> 文档，看能否解决疑惑 "
  DESC_ELSE: "若有其他问题/功能建议等到群反映/提issue"
  
  BrowserWindow_ensure_warning: "需要返回选择页，并确保有选择的情况下使用"
  jm_desc: "支持多车号输入，例如`123456，654321，114514`（逗号分隔）<br>支持章节下载，但仅限在读剪贴板流程上<br>剪贴板功能相关：不要复制`18comic.vip`这个域名(即翻墙后的jm)，莫名其妙有5秒盾，还不如直接输入车号<br>设置cookies能解锁游客隐藏，解决无效车号等，但CGS暂不会自动更新cookie，按需时粘贴curl用"
  wnacg_desc: "wancg 国内源偶尔会很慢，或者抽风，假如报错的话 <br>网络问题如 [Errno 11001 10054 10060]（httpx那块已内置重试8次） / `ReadTimeout` 一般重启就好了，<br>重启重试几次后还是一直出现同一种错误的话 加群反映/提issue<br>"
  mangabz_desc: "Māngabz 使用源为iphone网页版，逆天章节只有数字，例如第一卷和第一话都是1，需要根据相邻章节自己鉴别"
  hitomi_desc: "除预设外，输入请使用 hitomi-tools ，<a href='https://jsd.vxo.im/gh/jasoneri/imgur@main/CGS/hitomi-tools-usage.gif'>📹参考用法</a><br>1.不建议使用搜索，你的关键字极大可能无效 2.hitomi无法使用映射<br>【国内Tip】不用代理也能跑哦(￣﹃￣)震惊吧，不过预览时访问链接涉及官网域名的就不行了"
  check_ehetai: "正在检测当前环境能否访问 `exhentai` 中..."
  check_mangabz: "正在检测当前环境能否访问 `Māngabz` 中..."
  check_hitomi: "正在检测当前环境能否访问 `hitomi` 中..."
  checkisopen_text_change: "现在点击立刻打开存储目录"
  checkisopen_status_tip: "勾选状态下完成后也会自动打开目录的"
  ACCESS_FAIL: "当前`配置代理`或`全局代理`等环境无法访问<br>请前往网站访问排查（尚不支持该网站墙内直连）"
  cookies_copy_err: "格式错误，请看清楚最新gif的操作重新复制"
  copied_tip: "后台将复制[%s]条到剪贴板"
  textbrowser_load_if_http: "<b><font size=\"5\" color=\"black\">点击右下 `预览` </font></b><font color=\"black\"> 或者 </font><a href=\"%s\" ><b style=\"font-size:20px;\">浏览器查看结果</b></a>"
  WorkThread_finish_flag: "后台完成"
  WorkThread_empty_flag: "后台正常退出没有"
  copymaga_tips: "拷贝已解锁章节，例如福利连"
  copymaga_page_status_tip: "拷贝漫画的翻页数使用的offset/序号，一页30条，想翻到第3页就填60(输出60-89)，类推"
  global_err_hook: "刚才操作导致 GUI 发生异常, 详细查阅 GUI 日志"
  input_format_err: "输入格式错误，请鼠标移到输入框查看规则提示"
  reboot_tip: "♻️ 内置重启中: 正在清除残留后台进程.."
  reboot_tip2: "⛺️ 内置重启中: 正在重建ui与后台通讯管道.."
  SearchInputStatusTip:
    manga_copy: '（1）输入【搜索词】返回搜索结果（2）按空格弹出预设（2.1）规则补充：排名+日/周/月/总+轻小说/男/女，例如"排名轻小说月"'
    jm: '（1）输入【搜索词】返回搜索结果（2）按空格弹出预设（2.1）规则补充：时间维度可选 日/周/月/总，例如"收藏总"（3）翻页规则："&page=\d+"'
    wnacg: '（1）输入【搜索词】返回搜索结果（2）按空格弹出预设（3）翻页规则：搜索是"&p=\d+" 导航页是"-page-\d+" 如不满足请联系开发者'
    ehentai: '（1）输入【搜索词】返回搜索结果（2）按空格弹出预设'
    mangabz: '（1）输入【搜索词】返回搜索结果（2）按空格弹出预设'
    hitomi: '暂时仅支持使用预设 或 `hitomi-tools`生成的词'
  Clip:
    process_warning: "当前已进入搜索流程，使用此功能需重启并在搜索之前进行"
    db_not_found_guide: "剪贴板 db 不存在，请先查看相关指引"
    match_none: "无匹配任务，先进行复制再运行此功能，当前匹配规则：%s"
    get_info_error: "获取信息失败"
    partial_fail: "部分失败，但仍可继续处理任务窗口的任务"
    all_fail: "没有一个成功的任务，如http错误请更新配置如代理/cookies后重新运行此功能，若总是失败提issue"
    view_log: "在日志文件查看详细报错堆栈"
  Tools:
    rv_scriptp_desc: "rV脚本路径"
    rv_scriptp_desc_tip: "选择脚本文件（ %s ）"
    rv_deployBtn: "选择目录并部署"
    rv_deployDesc: "看起来你需要部署rV<br>1. 需选择与 CGS 和存储目录无关的非中文空目录<br>2. 根据控制台进行操作，部署后重新选择脚本"
    rv_deployWinRequire: "<br>3. win补充：控制面板 > 时钟与区域 > 区域 > 更改系统区域设置 > 勾选beta版 unicode UTF-8 > 重启"
    rv_deploy_desc: "rV 部署"
    rv_deploy_desc_content: "脚本检索异常，请选择正确的脚本或点击部署按钮进行部署"
    rv_book_marked: "显示记录"
    rv_merge_move: "整合转移"
    rv_combined_tip: "已将%s整合章节并转换至[%s]"
    rv_set_script_err: "设置rV脚本无效，点右侧先查阅rV一键部署方法"
    hitomi_tip_search: "在网站中搜索选中的tag"
    hitomi_tip_sv: "保存到预设"
    hitomi_tip_send: "发送到输入框"
    hitomi_tip_remove: "不使用tags，将强制使用排序"
    hitomi_tip_orderby: "[date:added] 无tag时等于index，有tag时相当于默认排序"
    hitomi_info_copied: "已复制，注意为 urldecode"
    hitomi_info_sved: "已保存 urlencode 后的值到预设之首"
    hitomi_info_sended: "已发送到搜索框，程序最适配urlencode值无需担心"
    domain_btn: "发布页"
    domain_desc: "点击发布页然后按图示复制你能用的部分（内地），<br>回来点击执行按键，CGS会花几秒检测是否可用并相应通知，<br>CGS并不能突破你的网络环境， 浏览器开不了CGS也开不了，请知悉"
    doamin_success_tip: "[%s]已设进下方临时文件，有效时间为48小时\n[%s]\n"
    doamin_error_tip: "域名全无效，请你自行检查，再次声明CGS并不能突破现网络环境去访问"
    status_desc: "功能1：显示各网的`远程最新版本代码`是否可用的状态，网络不稳定的话点 CGS 至官网查看；\n状态补充：当issue或群讨论后修改，会在对应网站状态里加*号，此时方可用功能2，否则别乱点可能导致瘫痪\n功能2：按钮组对应更新解密相关小变动，更新成功会重启（避免状态出来前点击）"
    status_fetching: "正在获取网站可用状态"
    status_waiting: "请等待一会~~"
    status_web_erratic: "[Warn]网络不稳定未能获取全部状态.."
    status_cf_error: "国内访问cloudflare的dev域名异常，可移步官网查看"
    status_no_update: "本地与线上内容一致"
    reboot_tip: "CGS将在%s秒后重启以确保后台进程应用有效更改"
  Uic:
    chooseBoxDefault: "选择网站"
    searchinputPlaceholderText: "输入关键字"
    chooseinputPlaceholderText: "输入序号"
    next_btnDefaultText: "搜索"
    checkisopenDefaultText: "(完成后自动)打开存储目录"
    chooseinputTip: "示例： 0 →全选(特殊)  |  2 →单选2  |  7+9 →多选 7、9 (加号)  |  3-5 →多选 3、4、5 (减号) |  1+7-9 →复合 多选 1、7、8、9 | -3 →倒数3个"
    chooseBoxToolTip: "选中网站后看状态栏有输入提示"
    previewBtnStatusTip: "点击打开预览窗口，仅当出现书列表后才能使用"
    progressBarStatusTip: " >>> Ⅰ、绿色100%表示完成 Ⅱ、 任务细化后查得绿色100%也有漏页情况，可结合`复制未完成`与`剪贴板功能`进行补页"
    sv_path_desc: "存储路径"
    sv_path_desc_tip: "选择目录"
    menu_show_completer: "展开预设"
    menu_next_page: "下一页"
    menu_prev_page: "上一页"
    confDia_labelLogLevel: "日志等级"
    confDia_labelDedup: "去重"
    confDia_labelAddUuid: "增加标识"
    confDia_labelProxy: "代理"
    confDia_labelMap: "映射"
    confDia_labelPreset: "预设"
    confDia_labelClipDb: "剪贴板db"
    confDia_labelClipNum: "读取条数"
    confDia_labelConcurrNum: "并发数"
    confDia_cookies_placeholder: "cookies获取方式参考配置说明页此栏目的动图\n需要包含以下字段：\n"
    confDia_svPathWarning: "设置存储路径失败，点击右侧查阅规则"
    confDia_descBtn: "使用手册"
    confDia_updateBtn: "检查更新"
    confDia_updateDialog_stable: "检测到最新稳定版"
    confDia_updateDialog_dev: "检测到最新开发版"
    confDia_supportBtn: "支持开发者"
    confDia_promote_title: "机场"
    confDia_promote_content: "永久流量等"
    confDia_promote_url: "https://hxlm.cc/#/register?code=UfFbIfOq"
    confDia_support_content: "👇请吃个甜筒吧..."

EHentai:
  COOKIES_NOT_SET: "访问 exhentai 必须设置`cookies-eh`"
  ACCESS_FAIL: "当前`cookies-eh`或`配置代理`或`全局代理`等环境无法访问<br>请前往网站访问排查（尚不支持该网站墙内直连）"
  GUIDE: "exhentai使用指引（里站，非表站）<br>1. 确保你有一个能访问`exhentai.org`的账号<br> - 配置需设置`cookies-eh`的值，值生成参考配置详情里该字段说明。<br>2. (国内)确保你有一个可以使用的代理（不支持无代理直连）<br> - 可使用全局代理，或者配置代理（若环境检测失败，配置后重启 CGS 即可）代理服务建议用v2rayN"
  JUMP_TIP: "ehentai跳页情况特殊，没想好应用，暂时设限制取消`跳页`功能"
  MAPPINGS_INDEX: "首页"
  MAPPINGS_POPULAR: "热门"

SPIDER:
  SayToGui:
    exp_txt: "请于【 输入序号 】框输入或框里右键菜单用点击输入要选的序号  "
    exp_turn_page: "搜索输入框右侧为`翻页按钮组`，分别是 上一页/下一页/输入页数跳转"
    exp_preview: "进预览页面能直接点击封面进行多选，预览页面右上确认选择(确认选择能额外与【输入序号】框的序号相叠加)"
    exp_replace_keyword: "请于【"
    TextBrowser_error: "选择{1}步骤时错误的输入：{0}<br> {2}"
    frame_book_print_extra: " →_→ 鼠标移到序号栏有教输入规则"
    frame_book_print_retry_tip: "什么意思？唔……就是你搜的在放✈(飞机)<br>翻页的话就是这页之后没列表了，非翻页的话看看浏览器链接是否也没 / 重开换种姿势再搜"
    frame_section_print_extra: " ←_← 点击【开始爬取！】"
  chooseInput_flag: "输入要选的序号"
  sectionInput_flag: "开始爬取"
  search_url_head_NotImplementedError: "需要自定义搜索网址"
  choice_list_before_turn_page: "此前页面已选择(待结算)"
  parse_step: "漫画"
  parse_sec_step: "章节"
  parse_sec_not_match: "没匹配到结果"
  parse_sec_selected: "所选序号"
  parse_sec_now_start_crawl_desc: "现在开始爬取《%s》章节"
  page_less_than_one: "当前页数少于1，避免出错设置回第一页"
  finished_success: "~~~后台完成[%s]个图片任务了 ヾ(￣▽￣ )Bye~Bye~"
  finished_err: "~~~…(￣┰￣*)………后台异常退出，最后捕捉的异常信息为<br>%s"
  finished_empty: "~~~…(￣┰￣*)………后台正常退出没有产生图片任务，请自行检查输入"
  close_backend_error: "~~~…(￣┰￣*)………后台挂了，排错操作指引如下"
  close_check_log_guide1: "1、打开下方的日志文件，查阅看有没开发者定义了的错误提示，或者是重试能解决的网络问题如 ReadTimeout"
  close_check_log_guide2: "2、第1步非网络问题的话，重启(retry)程序 > 更改配置 > 日志等级设为`DEBUG` > 重复引发出错的步骤"
  close_check_log_guide3: "3、第2步得出的日志同一种错误一直重复的话，请到群反映或提issue"
  ERO_BOOK_FOLDER: "本子"
  PUBLISH_INVALID: "<a href=\"%s\">发布页</a>访问失效，<br>(简单)建议点击 rV 按钮 > domainTool 根据指示操作<br>(稍微复杂)或参考<a href=\"https://jasoneri.github.io/ComicGUISpider/faq/extra\">📒域名相关</a>处理[%s])"
  DOMAINS_INVALID: "<a href=\"%s\">发布页</a>清洗出的域名%s访问失效，<br>(简单)建议点击 rV 按钮 > domainTool 根据指示操作<br>(稍微复杂)或参考<a href=\"https://jasoneri.github.io/ComicGUISpider/faq/extra\">📒域名相关</a>处理[%s])<br>或通过 domainTool 进行操作"

Updater:
  ver_check: "检查版本中"
  ver_file_not_exist: "没有version文件，准备初始化"
  check_refresh_code: "检查需要更新的代码"
  code_downloading: "下载代码文件中"
  finish: "更新完毕"
  not_pkg_markdown: "当前环境无法使用此功能，需要重新下载绿色安装包"
  git_update_desc: "检测为git环境，需自行git pull并自行更新依赖"
  token_invalid_notification: "[ 本地文件的token全部失效/读取失误，当前将使用无状态去请求github api（受限60请求/小时）]\n下次使用更新会重新下载token文件，还是全部失效/读取失误的话可截图告知开发者"
  latest_code_overwriting: "使用最新版本代码覆盖中"
  too_much_waiting_update: "检测到堆积过多待更新版本，将忽略更新消息直接拉至最新版本代码"
  refreshing_code: "更新代码中"
  refresh_fail_retry: "更新失败, 准备重试"
  refresh_fail_retry_over_limit: "是网络问题，重试更新即可。 若其他情况导致更新一直失败请截图发issue或找群反映"
  code_is_latest: "代码已是最新.. 若有其他问题向群里反映"
  env_is_latest: "环境已是最新"
  ver_local_latest: "本地版本已是最新.."
  ver_check_fail: "检查版本失败，可重试或前往releases页面查看"
  update_ensure: "确认更新"
  doing: "过程会涉及代码与依赖的更新，请稍等片刻.."
  updated_success: "更新成功，CGS将在5s后重启"
  updated_fail: "❌ 更新失败，已使用回溯方案保证程序正常使用\n可等会重试或点击链接前往下载（CGS将在10s后重启）\n报错日志为[%s]" 
